"""
وحدة إدارة المباني والمرافق
Buildings and Facilities Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from datetime import datetime

class BuildingsManagementWindow:
    """نافذة إدارة المباني والمرافق"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.buildings_tree = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.tree_font = ("Segoe UI", 11)

    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح في PDF"""
        try:
            # إزالة الرموز التعبيرية التي قد تسبب مشاكل
            text = text.replace("🏢", "").replace("📊", "").replace("📋", "").replace("📅", "")
            text = text.replace("🏗️", "").replace("•", "-")

            # تنظيف النص
            text = text.strip()

            # ترميز النص بـ UTF-8
            if isinstance(text, str):
                text = text.encode('utf-8').decode('utf-8')

            return text
        except Exception as e:
            print(f"خطأ في تنسيق النص العربي: {e}")
            return str(text)

    def setup_arabic_pdf_support(self):
        """إعداد دعم اللغة العربية في PDF مع خطوط متعددة محسنة"""
        try:
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import os
            import platform

            self.registered_fonts = {}

            # قائمة الخطوط العربية المدعومة حسب نظام التشغيل
            if platform.system() == "Windows":
                font_paths = {
                    'Arabic-Bold': [
                        "C:/Windows/Fonts/arialbd.ttf",
                        "C:/Windows/Fonts/tahomabd.ttf",
                        "C:/Windows/Fonts/calibrib.ttf"
                    ],
                    'Arabic-Regular': [
                        "C:/Windows/Fonts/arial.ttf",
                        "C:/Windows/Fonts/tahoma.ttf",
                        "C:/Windows/Fonts/calibri.ttf",
                        "C:/Windows/Fonts/segoeui.ttf"
                    ],
                    'Arabic-Italic': [
                        "C:/Windows/Fonts/ariali.ttf",
                        "C:/Windows/Fonts/calibrii.ttf"
                    ]
                }
            else:
                # مسارات الخطوط في لينكس/ماك
                font_paths = {
                    'Arabic-Bold': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                        "/System/Library/Fonts/Arial Bold.ttf"
                    ],
                    'Arabic-Regular': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                        "/System/Library/Fonts/Arial.ttf"
                    ],
                    'Arabic-Italic': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Oblique.ttf"
                    ]
                }

            # تسجيل الخطوط المتوفرة
            for font_name, paths in font_paths.items():
                for font_path in paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont(font_name, font_path))
                            self.registered_fonts[font_name] = font_name
                            print(f"✅ تم تسجيل الخط: {font_name} من {font_path}")
                            break
                        except Exception as e:
                            print(f"⚠️ فشل في تسجيل {font_name}: {e}")
                            continue

            # التأكد من وجود خط أساسي على الأقل
            if not self.registered_fonts:
                print("⚠️ لم يتم العثور على خطوط عربية، سيتم استخدام الخط الافتراضي")
                # إضافة خطوط احتياطية
                self.registered_fonts = {
                    'Arabic-Bold': 'Helvetica-Bold',
                    'Arabic-Regular': 'Helvetica',
                    'Arabic-Italic': 'Helvetica-Oblique'
                }
                return False

            return True

        except Exception as e:
            print(f"❌ خطأ في إعداد الخطوط العربية: {e}")
            # إضافة خطوط احتياطية في حالة الخطأ
            self.registered_fonts = {
                'Arabic-Bold': 'Helvetica-Bold',
                'Arabic-Regular': 'Helvetica',
                'Arabic-Italic': 'Helvetica-Oblique'
            }
            return False

    def show(self):
        """عرض نافذة إدارة المباني"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🏢 إدارة المباني والمرافق")
        self.window.geometry("1000x600")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        self.create_interface()
        self.load_buildings()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة إدارة المباني"""
        # عنوان الصفحة
        header_frame = ttk_bs.Frame(self.window)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        title_label = ttk_bs.Label(
            header_frame,
            text="🏢 إدارة المباني والمرافق 🏢",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(side=tk.LEFT)

        # شريط الأدوات
        toolbar_frame = ttk_bs.LabelFrame(
            self.window,
            text="🛠️ أدوات المباني",
            padding=15,
            bootstyle="info"
        )
        toolbar_frame.pack(fill=tk.X, padx=15, pady=10)

        # الأزرار
        ttk_bs.Button(
            toolbar_frame,
            text="➕ مبنى جديد",
            command=self.add_building,
            bootstyle="success",
            width=18
        ).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_building,
            bootstyle="warning",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_building,
            bootstyle="danger",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="📋 تفاصيل",
            command=self.view_building_details,
            bootstyle="info",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_buildings,
            bootstyle="secondary",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🖨️ طباعة PDF",
            command=self.print_buildings_report,
            bootstyle="primary",
            width=18).pack(side=tk.LEFT, padx=8, ipady=8)
        
        # شريط البحث
        search_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔍 البحث والفلترة",
            padding=15,
            bootstyle="secondary"
        )
        search_frame.pack(fill=tk.X, padx=15, pady=10)

        ttk_bs.Label(
            search_frame,
            text="🔍 البحث"
        ).pack(side=tk.LEFT, padx=(0, 5))

        self.search_entry = ttk_bs.Entry(
            search_frame,
            width=40,
            bootstyle="info"
        )
        self.search_entry.pack(side=tk.LEFT, padx=5, ipady=3)
        self.search_entry.bind('<KeyRelease>', self.filter_buildings)

        ttk_bs.Button(
            search_frame,
            text="🔍 بحث",
            command=self.filter_buildings,
            bootstyle="info",
            width=10
        ).pack(side=tk.LEFT, padx=5, ipady=3)
        
        # جدول المباني
        tree_frame = ttk_bs.LabelFrame(
            self.window,
            text="🏢 قائمة المباني والمرافق",
            padding=15,
            bootstyle="primary"
        )
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # إنشاء Treeview
        columns = ("id", "name", "building_type", "department_name", "section_name", "location", "floors", "area", "owner", "construction_year")
        self.buildings_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تطبيق خط على الجدول
        style = ttk.Style()
        style.configure("Treeview", rowheight=30)
        style.configure("Treeview.Heading", foreground="#1e3a8a")

        # تعريف العناوين
        headers = {
            "id": "🆔 المعرف",
            "name": "🏢 اسم المبنى",
            "building_type": "🏗️ النوع",
            "department_name": "🏛️ الإدارة",
            "section_name": "🏢 القسم",
            "location": "📍 الموقع",
            "floors": "🏢 الطوابق",
            "area": "📐 المساحة",
            "owner": "👤 المالك",
            "construction_year": "📅 سنة البناء"
        }
        
        for col, header in headers.items():
            self.buildings_tree.heading(col, text=header)
            if col == "id":
                self.buildings_tree.column(col, width=50)
            elif col in ["name", "location"]:
                self.buildings_tree.column(col, width=150)
            elif col in ["department_name", "section_name"]:
                self.buildings_tree.column(col, width=120)
            else:
                self.buildings_tree.column(col, width=100)
        
        # شريط التمرير
        scrollbar_y = tk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.buildings_tree.yview)
        scrollbar_x = tk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.buildings_tree.xview)
        self.buildings_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تعبئة الجدول وشريط التمرير
        self.buildings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط النقر المزدوج
        self.buildings_tree.bind('<Double-1>', lambda e: self.view_building_details())
    
    def load_buildings(self):
        """تحميل المباني من قاعدة البيانات"""
        if not self.buildings_tree:
            return

        # مسح البيانات الحالية
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)

        try:
            # قراءة البيانات من قاعدة البيانات
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود الأعمدة الجديدة أولاً
            cursor.execute("PRAGMA table_info(buildings)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'department_name' in columns and 'section_name' in columns:
                cursor.execute('''
                    SELECT id, name, building_type, department_name, section_name, location, floors, area, owner, construction_year
                    FROM buildings
                    ORDER BY name
                ''')
            else:
                cursor.execute('''
                    SELECT id, name, building_type, location, floors, area, owner, construction_year
                    FROM buildings
                    ORDER BY name
                ''')

            buildings = cursor.fetchall()

            for building in buildings:
                # تنسيق البيانات للعرض
                display_building = list(building)

                # التأكد من وجود قيم للحقول الاختيارية
                for i in range(len(display_building)):
                    if display_building[i] is None:
                        display_building[i] = ""

                self.buildings_tree.insert('', 'end', values=display_building)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل المباني: {e}")
            # في حالة الخطأ، عرض بيانات تجريبية محسنة
            self.load_sample_buildings()

    def load_sample_buildings(self):
        """تحميل بيانات وهمية للمباني للعرض"""
        sample_buildings = [
            (1, "المبنى الإداري الرئيسي", "إداري", "الحرم الجامعي الرئيسي", 5, 2500.0, "الجامعة", 2020),
            (2, "مبنى كلية الهندسة", "أكاديمي", "الحرم الجامعي الشمالي", 4, 3200.0, "الجامعة", 2018),
            (3, "مبنى المختبرات والورش", "تقني", "منطقة المختبرات", 3, 1800.0, "الجامعة", 2019),
            (4, "مبنى الخدمات العامة", "خدمي", "المنطقة الخدمية", 2, 1200.0, "الجامعة", 2015),
            (5, "مبنى المكتبة المركزية", "تعليمي", "وسط الحرم الجامعي", 4, 2800.0, "الجامعة", 2021),
            (6, "مبنى الأمن والسلامة", "أمني", "المدخل الرئيسي", 2, 800.0, "الجامعة", 2017),
            (7, "مبنى الصيانة والتشغيل", "خدمي", "المنطقة الخلفية", 1, 600.0, "الجامعة", 2016),
            (8, "مبنى الطلاب والأنشطة", "اجتماعي", "منطقة الأنشطة", 3, 2200.0, "الجامعة", 2022)
        ]

        for building in sample_buildings:
            building_id, name, building_type, location, floors, area, owner, construction_year = building

            # تنسيق البيانات للعرض
            display_building = [
                building_id, name, building_type, location,
                str(floors), f"{area:,.0f}", owner, str(construction_year)
            ]

            self.buildings_tree.insert('', 'end', values=display_building)
    
    def filter_buildings(self, event=None):
        """فلترة المباني حسب النص المدخل"""
        search_text = self.search_entry.get().lower()

        # مسح البيانات الحالية
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)

        try:
            # قراءة البيانات من قاعدة البيانات مع الفلترة
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            if search_text:
                # فلترة حسب النص
                cursor.execute('''
                    SELECT id, name, building_type, location, floors, area, owner, construction_year
                    FROM buildings
                    WHERE LOWER(name) LIKE ? OR LOWER(building_type) LIKE ? OR LOWER(location) LIKE ?
                    ORDER BY name
                ''', (f'%{search_text}%', f'%{search_text}%', f'%{search_text}%'))
            else:
                # عرض جميع المباني
                cursor.execute('''
                    SELECT id, name, building_type, location, floors, area, owner, construction_year
                    FROM buildings
                    ORDER BY name
                ''')

            buildings = cursor.fetchall()

            for building in buildings:
                # تنسيق البيانات للعرض
                display_building = list(building)

                # التأكد من وجود قيم للحقول الاختيارية
                for i in range(len(display_building)):
                    if display_building[i] is None:
                        display_building[i] = ""

                self.buildings_tree.insert('', 'end', values=display_building)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فلترة المباني: {str(e)}")
            # في حالة الخطأ، إعادة تحميل جميع البيانات
            self.load_buildings()
    
    def add_building(self):
        """إضافة مبنى جديد"""
        dialog = BuildingDialog(self.window, self.db_manager, self.auth_manager, "إضافة مبنى جديد")
        if dialog.show():
            self.load_buildings()
    
    def edit_building(self):
        """تعديل مبنى محدد"""
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى للتعديل")
            return
        
        building_data = self.buildings_tree.item(selected[0])['values']
        dialog = BuildingDialog(self.window, self.db_manager, self.auth_manager, "تعديل المبنى", building_data)
        if dialog.show():
            self.load_buildings()
    
    def delete_building(self):
        """حذف مبنى محدد"""
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى للحذف")
            return

        building_id = self.buildings_tree.item(selected[0])['values'][0]
        building_name = self.buildings_tree.item(selected[0])['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المبنى '{building_name}'؟\nسيتم حذف جميع البيانات المرتبطة به."):
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()

                # حذف البيانات المرتبطة أولاً
                cursor.execute("DELETE FROM maintenance_requests WHERE building_id = ?", (building_id,))
                cursor.execute("DELETE FROM assets WHERE building_id = ?", (building_id,))
                cursor.execute("DELETE FROM documents WHERE entity_type = 'building' AND entity_id = ?", (building_id,))

                # حذف المبنى
                cursor.execute("DELETE FROM buildings WHERE id = ?", (building_id,))

                conn.commit()
                messagebox.showinfo("نجح", "تم حذف المبنى بنجاح")
                self.load_buildings()

                conn.close()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المبنى: {str(e)}")
    
    def view_building_details(self):
        """عرض تفاصيل المبنى"""
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى لعرض التفاصيل")
            return
        
        building_data = self.buildings_tree.item(selected[0])['values']
        details = f"""
🏢 تفاصيل المبنى

🆔 المعرف {building_data[0]}
🏢 الاسم {building_data[1]}
🏗️ النوع {building_data[2]}
📍 الموقع {building_data[3]}
🏢 عدد الطوابق {building_data[4]}
📐 المساحة {building_data[5]}
📊 الحالة {building_data[6]}
📅 سنة البناء {building_data[7]}
        """
        messagebox.showinfo("تفاصيل المبنى", details)

    def print_buildings_report(self):
        """طباعة تقرير المباني مع معاينة محسنة"""
        try:
            # الحصول على بيانات المباني
            buildings_data = self.get_buildings_data_for_report()

            if not buildings_data:
                messagebox.showwarning("تحذير", "لا توجد مباني لطباعتها")
                return

            # إنشاء معاينة الطباعة
            self.show_print_preview(buildings_data)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء معاينة الطباعة:\n{str(e)}")

    def show_print_preview(self, buildings_data):
        """عرض معاينة الطباعة المحسنة"""
        import tempfile
        import webbrowser
        from datetime import datetime

        # إنشاء HTML للمعاينة
        html_content = self.create_buildings_html_report(buildings_data)

        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(html_content)
            temp_file = f.name

        # فتح المعاينة في المتصفح
        try:
            webbrowser.open(f'file://{temp_file}')
        except Exception as e:
            messagebox.showerror("خطأ", f"لا يمكن فتح معاينة الطباعة:\n{str(e)}")

    def create_buildings_html_report(self, buildings_data):
        """إنشاء تقرير HTML محسن للمباني"""
        from datetime import datetime

        current_date = datetime.now().strftime("%Y/%m/%d")
        current_time = datetime.now().strftime("%H:%M")

        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير إدارة المباني والمرافق</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap');

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Tajawal', 'Segoe UI', 'Tahoma', Arial, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}

        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }}

        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }}

        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="buildings" width="50" height="50" patternUnits="userSpaceOnUse"><rect x="10" y="20" width="8" height="20" fill="rgba(255,255,255,0.1)"/><rect x="25" y="15" width="8" height="25" fill="rgba(255,255,255,0.1)"/><rect x="40" y="18" width="8" height="22" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23buildings)"/></svg>');
            opacity: 0.2;
        }}

        .header h1 {{
            font-size: 2.8em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }}

        .header .subtitle {{
            font-size: 1.3em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }}

        .info-bar {{
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            padding: 25px 40px;
            border-bottom: 4px solid #34495e;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }}

        .info-item {{
            display: flex;
            align-items: center;
            margin: 8px 0;
            background: white;
            padding: 10px 15px;
            border-radius: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}

        .info-item i {{
            margin-left: 10px;
            color: #2c3e50;
            font-size: 1.3em;
        }}

        .content {{
            padding: 40px;
        }}

        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }}

        .stat-card {{
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }}

        .stat-card:hover {{
            transform: translateY(-5px);
        }}

        .stat-card.excellent {{
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }}

        .stat-card.good {{
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }}

        .stat-card.poor {{
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }}

        .stat-number {{
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 8px;
        }}

        .stat-label {{
            font-size: 1em;
            opacity: 0.95;
        }}

        .table-container {{
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }}

        table {{
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }}

        th {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 18px 12px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }}

        td {{
            padding: 15px 12px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            vertical-align: middle;
        }}

        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}

        tr:hover {{
            background-color: #e8f4fd;
            transform: scale(1.01);
            transition: all 0.3s ease;
        }}

        .condition {{
            padding: 8px 15px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 0.9em;
            text-transform: uppercase;
        }}

        .condition.excellent {{
            background: #d5f4e6;
            color: #27ae60;
            border: 2px solid #2ecc71;
        }}

        .condition.good {{
            background: #fef9e7;
            color: #f39c12;
            border: 2px solid #e67e22;
        }}

        .condition.poor {{
            background: #fadbd8;
            color: #e74c3c;
            border: 2px solid #c0392b;
        }}

        .building-type {{
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.85em;
        }}

        .building-type.administrative {{
            background: #e8f5e8;
            color: #2e7d32;
        }}

        .building-type.academic {{
            background: #e3f2fd;
            color: #1976d2;
        }}

        .building-type.technical {{
            background: #fff3e0;
            color: #f57c00;
        }}

        .building-type.service {{
            background: #f3e5f5;
            color: #7b1fa2;
        }}

        .footer {{
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            padding: 25px 40px;
            text-align: center;
            border-top: 4px solid #34495e;
            color: #2c3e50;
        }}

        /* تنسيق خاص للطباعة */
        @media print {{
            body {{
                background: white;
                padding: 0;
            }}

            .container {{
                box-shadow: none;
                border-radius: 0;
            }}

            .header {{
                background: #2c3e50 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            .stat-card {{
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            th {{
                background: #2c3e50 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            tr:nth-child(even) {{
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            .condition, .building-type {{
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            @page {{
                size: A4 landscape;
                margin: 1cm;
            }}
        }}

        .print-button {{
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            border: none;
            padding: 18px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }}

        .print-button:hover {{
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }}

        @media print {{
            .print-button {{
                display: none;
            }}
        }}
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">🖨️ طباعة التقرير</button>

    <div class="container">
        <div class="header">
            <h1>🏢 تقرير إدارة المباني والمرافق</h1>
            <div class="subtitle">نظام إدارة أعمال الإدارة الهندسية</div>
        </div>

        <div class="info-bar">
            <div class="info-item">
                <i>📅</i>
                <span>تاريخ التقرير: {current_date}</span>
            </div>
            <div class="info-item">
                <i>⏰</i>
                <span>وقت الإنشاء: {current_time}</span>
            </div>
            <div class="info-item">
                <i>🏗️</i>
                <span>إجمالي المباني: {len(buildings_data)}</span>
            </div>
        </div>

        <div class="content">"""

        # إضافة إحصائيات
        stats = self.calculate_buildings_stats(buildings_data)
        html += f"""
            <div class="stats-grid">
                <div class="stat-card excellent">
                    <div class="stat-number">{stats['excellent']}</div>
                    <div class="stat-label">حالة ممتازة</div>
                </div>
                <div class="stat-card good">
                    <div class="stat-number">{stats['good']}</div>
                    <div class="stat-label">حالة جيدة</div>
                </div>
                <div class="stat-card poor">
                    <div class="stat-number">{stats['poor']}</div>
                    <div class="stat-label">تحتاج صيانة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats['total_area']:,.0f}</div>
                    <div class="stat-label">إجمالي المساحة (م²)</div>
                </div>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم المبنى</th>
                            <th>النوع</th>
                            <th>الموقع</th>
                            <th>الطوابق</th>
                            <th>المساحة (م²)</th>
                            <th>سنة البناء</th>
                            <th>الحالة الإنشائية</th>
                            <th>المالك</th>
                        </tr>
                    </thead>
                    <tbody>"""

        # إضافة بيانات الجدول
        for building in buildings_data:
            condition_class = self.get_condition_class(building.get('structural_condition', ''))
            type_class = self.get_building_type_class(building.get('building_type', ''))

            html += f"""
                        <tr>
                            <td><strong>{building.get('name', 'غير محدد')}</strong></td>
                            <td><span class="building-type {type_class}">{building.get('building_type', 'غير محدد')}</span></td>
                            <td>{building.get('location', 'غير محدد')}</td>
                            <td>{building.get('floors', 'غير محدد')}</td>
                            <td>{building.get('area', 0):,.0f}</td>
                            <td>{building.get('construction_year', 'غير محدد')}</td>
                            <td><span class="condition {condition_class}">{building.get('structural_condition', 'غير محدد')}</span></td>
                            <td>{building.get('owner', 'غير محدد')}</td>
                        </tr>"""

        html += """
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            <p><strong>نظام إدارة أعمال الإدارة الهندسية</strong></p>
            <p>تم إنشاء هذا التقرير تلقائياً • جميع الحقوق محفوظة</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>"""

        return html

    def calculate_buildings_stats(self, buildings_data):
        """حساب إحصائيات المباني"""
        stats = {
            'excellent': 0,
            'good': 0,
            'poor': 0,
            'total_area': 0
        }

        for building in buildings_data:
            condition = building.get('structural_condition', '').lower()
            area = building.get('area', 0)

            if isinstance(area, (int, float)):
                stats['total_area'] += area

            if 'ممتاز' in condition or 'excellent' in condition:
                stats['excellent'] += 1
            elif 'جيد' in condition or 'good' in condition:
                stats['good'] += 1
            else:
                stats['poor'] += 1

        return stats

    def get_condition_class(self, condition):
        """الحصول على فئة CSS للحالة الإنشائية"""
        condition = condition.lower()
        if 'ممتاز' in condition or 'excellent' in condition:
            return 'excellent'
        elif 'جيد' in condition or 'good' in condition:
            return 'good'
        else:
            return 'poor'

    def get_building_type_class(self, building_type):
        """الحصول على فئة CSS لنوع المبنى"""
        building_type = building_type.lower()
        if 'إداري' in building_type or 'administrative' in building_type:
            return 'administrative'
        elif 'أكاديمي' in building_type or 'academic' in building_type:
            return 'academic'
        elif 'تقني' in building_type or 'technical' in building_type:
            return 'technical'
        elif 'خدمي' in building_type or 'service' in building_type:
            return 'service'
        return 'administrative'

    def get_buildings_data_for_report(self):
        """الحصول على بيانات المباني الفعلية من قاعدة البيانات للتقرير"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود الأعمدة في الجدول
            cursor.execute("PRAGMA table_info(buildings)")
            columns = [column[1] for column in cursor.fetchall()]

            # استعلام شامل للحصول على جميع بيانات المباني
            base_columns = ['id', 'name', 'building_type', 'location', 'floors', 'area', 'construction_year', 'owner', 'structural_condition']
            available_columns = [col for col in base_columns if col in columns]

            # إنشاء الاستعلام بناءً على الأعمدة المتاحة
            select_clause = ', '.join(available_columns)
            cursor.execute(f'''
                SELECT {select_clause}
                FROM buildings
                ORDER BY name
            ''')

            buildings = cursor.fetchall()

            # تحويل البيانات إلى تنسيق مناسب للتقرير
            buildings_data = []

            for building in buildings:
                building_dict = {}
                for i, col in enumerate(available_columns):
                    value = building[i] if i < len(building) else None

                    # معالجة القيم الفارغة أو None
                    if value is None or value == '':
                        if col in ['floors', 'area', 'construction_year']:
                            building_dict[col] = 0
                        else:
                            building_dict[col] = 'غير محدد'
                    else:
                        building_dict[col] = value

                # التأكد من وجود جميع الحقول المطلوبة
                required_fields = {
                    'name': 'غير محدد',
                    'building_type': 'غير محدد',
                    'location': 'غير محدد',
                    'floors': 0,
                    'area': 0,
                    'construction_year': 0,
                    'owner': 'غير محدد',
                    'structural_condition': 'غير محدد'
                }

                for field, default_value in required_fields.items():
                    if field not in building_dict:
                        building_dict[field] = default_value

                buildings_data.append(building_dict)

            conn.close()

            # إذا لم توجد بيانات، إرجاع رسالة توضيحية
            if not buildings_data:
                return [
                    {
                        'name': 'لا توجد مباني مسجلة',
                        'building_type': 'غير محدد',
                        'location': 'غير محدد',
                        'floors': 0,
                        'area': 0,
                        'construction_year': 0,
                        'owner': 'غير محدد',
                        'structural_condition': 'غير محدد'
                    }
                ]

            return buildings_data

        except Exception as e:
            print(f"خطأ في جلب بيانات المباني: {str(e)}")
            # في حالة الخطأ، إرجاع بيانات توضيحية
            return [
                {
                    'name': 'خطأ في تحميل البيانات',
                    'building_type': 'غير محدد',
                    'location': 'غير محدد',
                    'floors': 0,
                    'area': 0,
                    'construction_year': 0,
                    'owner': 'غير محدد',
                    'structural_condition': f'خطأ: {str(e)}'
                }
            ]

    def create_buildings_pdf_report(self, buildings_data, file_path):
        """إنشاء تقرير PDF احترافي أفقي للمباني مع دعم كامل للعربية"""
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
        from datetime import datetime

        # إعداد الخطوط العربية المحسنة
        self.setup_arabic_pdf_support()

        # إنشاء مستند PDF بتخطيط أفقي محسن للطباعة
        doc = SimpleDocTemplate(
            file_path,
            pagesize=landscape(A4),  # تخطيط أفقي
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=1.5*cm,
            title="تقرير المباني والمرافق",
            author="نظام إدارة أعمال الإدارة الهندسية",
            subject="تقرير إداري أفقي - المباني والمرافق",
            creator="Engineering Management System - Buildings Landscape Report"
        )

        # إنشاء الأنماط المحسنة للتخطيط الأفقي
        styles = getSampleStyleSheet()

        # تحديد الخطوط المناسبة للتخطيط الأفقي
        if hasattr(self, 'registered_fonts') and self.registered_fonts:
            title_font = self.registered_fonts.get('Arabic-Bold', 'Helvetica-Bold')
            heading_font = self.registered_fonts.get('Arabic-Bold', 'Helvetica-Bold')
            normal_font = self.registered_fonts.get('Arabic-Regular', 'Helvetica')
        else:
            title_font = heading_font = 'Helvetica-Bold'
            normal_font = 'Helvetica'

        # أنماط احترافية محسنة للتخطيط الأفقي
        title_style = ParagraphStyle(
            'LandscapeTitle',
            parent=styles['Title'],
            fontSize=24,  # خط أكبر للتخطيط الأفقي
            spaceAfter=25,
            alignment=TA_CENTER,
            textColor=colors.Color(0.12, 0.23, 0.58),  # أزرق داكن احترافي
            fontName=title_font,
            backColor=colors.Color(0.95, 0.95, 0.95),  # خلفية رمادية فاتحة
            borderWidth=2,
            borderColor=colors.Color(0.12, 0.23, 0.58),
            leftIndent=10,
            rightIndent=10,
            topPadding=10,
            bottomPadding=10
        )

        heading_style = ParagraphStyle(
            'LandscapeHeading',
            parent=styles['Heading1'],
            fontSize=16,  # خط أكبر للعناوين الفرعية
            spaceAfter=15,
            alignment=TA_RIGHT,
            textColor=colors.Color(0.0, 0.5, 0.0),  # أخضر داكن
            fontName=heading_font,
            backColor=colors.Color(0.9, 1.0, 0.9),  # خلفية خضراء فاتحة
            leftIndent=5,
            rightIndent=5,
            topPadding=5,
            bottomPadding=5
        )

        # نمط النصوص العادية المحسن
        normal_style = ParagraphStyle(
            'LandscapeNormal',
            parent=styles['Normal'],
            fontSize=12,  # خط أكبر للنصوص العادية
            spaceAfter=8,
            alignment=TA_RIGHT,
            fontName=normal_font,
            leading=16  # تباعد أكبر بين الأسطر
        )

        # نمط التاريخ
        date_style = ParagraphStyle(
            'LandscapeDate',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            alignment=TA_LEFT,
            fontName=normal_font,
            textColor=colors.Color(0.4, 0.4, 0.4)  # رمادي
        )

        # قائمة العناصر
        story = []

        # العنوان الرئيسي مع تصميم احترافي
        story.append(Paragraph("🏢 تقرير المباني والمرافق - تخطيط أفقي محسن 🏢", title_style))
        story.append(Spacer(1, 25))

        # التاريخ مع تنسيق محسن
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        story.append(Paragraph(f"📅 تاريخ إنشاء التقرير {current_time}", date_style))
        story.append(Spacer(1, 20))

        # إحصائيات سريعة مع تصميم محسن
        total_buildings = len(buildings_data)
        total_area = sum([b['area'] for b in buildings_data])
        avg_floors = sum([b['floors'] for b in buildings_data]) / total_buildings if total_buildings > 0 else 0

        story.append(Paragraph("📊 الإحصائيات العامة للمباني والمرافق", heading_style))
        story.append(Spacer(1, 10))
        story.append(Paragraph(f"• إجمالي المباني المسجلة: {total_buildings} مبنى", normal_style))
        story.append(Paragraph(f"• إجمالي المساحة الإجمالية: {total_area:,.0f} متر مربع", normal_style))
        story.append(Paragraph(f"• متوسط عدد الطوابق: {avg_floors:.1f} طابق", normal_style))
        story.append(Spacer(1, 25))

        # جدول المباني مع تصميم احترافي أفقي
        story.append(Paragraph("📋 جدول تفاصيل المباني والمرافق", heading_style))
        story.append(Spacer(1, 15))

        # إنشاء بيانات الجدول مع أعمدة محسنة للتخطيط الأفقي
        table_data = [['اسم المبنى', 'النوع', 'الموقع', 'الطوابق', 'المساحة (م²)', 'المالك', 'سنة البناء', 'الحالة']]

        for building in buildings_data:
            # تحديد لون الحالة
            condition = building.get('structural_condition', 'جيد')

            table_data.append([
                building['name'],
                building['building_type'],
                building.get('location', 'غير محدد'),
                str(building['floors']),
                f"{building['area']:,.0f}",
                building.get('owner', 'غير محدد'),
                str(building.get('construction_year', 'غير محدد')),
                condition
            ])

        # إنشاء الجدول مع عرض أعمدة محسن للتخطيط الأفقي
        col_widths = [3.5*cm, 2.5*cm, 2.5*cm, 1.5*cm, 2*cm, 2*cm, 1.8*cm, 2*cm]  # عرض مناسب للتخطيط الأفقي
        table = Table(table_data, colWidths=col_widths, repeatRows=1)

        # تطبيق تصميم احترافي محسن
        table.setStyle(TableStyle([
            # تنسيق الرأس - أزرق داكن احترافي
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.12, 0.23, 0.58)),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), heading_font),
            ('FONTSIZE', (0, 0), (-1, 0), 12),  # خط أكبر للرؤوس
            ('BOTTOMPADDING', (0, 0), (-1, 0), 15),
            ('TOPPADDING', (0, 0), (-1, 0), 15),

            # تنسيق البيانات - خط أكبر وتباعد محسن
            ('FONTNAME', (0, 1), (-1, -1), normal_font),
            ('FONTSIZE', (0, 1), (-1, -1), 11),  # خط أكبر للبيانات
            ('GRID', (0, 0), (-1, -1), 1.5, colors.Color(0.3, 0.3, 0.3)),  # حدود أوضح
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.Color(0.95, 0.95, 0.95)]),  # صفوف متناوبة
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 10),

            # تمييز لوني للحالات
            ('TEXTCOLOR', (7, 1), (7, -1), colors.Color(0.0, 0.6, 0.0)),  # أخضر للحالة الجيدة
        ]))

        story.append(table)
        story.append(Spacer(1, 30))

        # إضافة تذييل احترافي
        footer_style = ParagraphStyle(
            'LandscapeFooter',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            fontName=normal_font,
            textColor=colors.Color(0.4, 0.4, 0.4),
            spaceAfter=10
        )

        story.append(Paragraph("=" * 100, footer_style))
        story.append(Spacer(1, 10))
        story.append(Paragraph("🏗️ نظام إدارة أعمال الإدارة الهندسية - تقرير المباني والمرافق", footer_style))
        story.append(Paragraph("Engineering Management System - Buildings & Facilities Report", footer_style))
        story.append(Paragraph(f"تم إنشاء هذا التقرير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", footer_style))

        # بناء المستند مع معالجة الأخطاء
        try:
            doc.build(story, onFirstPage=self.add_page_number, onLaterPages=self.add_page_number)

            # فتح نافذة الطباعة تلقائياً (اختياري)
            self.auto_print_pdf(file_path)

        except Exception as e:
            raise Exception(f"فشل في بناء مستند PDF: {str(e)}")

    def add_page_number(self, canvas, doc):
        """إضافة رقم الصفحة"""
        try:
            from reportlab.lib.units import cm
            canvas.saveState()
            canvas.setFont('Helvetica', 10)
            page_num = canvas.getPageNumber()
            text = f"صفحة {page_num}"
            canvas.drawRightString(doc.pagesize[0] - 2*cm, 1*cm, text)
            canvas.restoreState()
        except:
            pass

    def auto_print_pdf(self, file_path):
        """فتح نافذة الطباعة تلقائياً"""
        try:
            import platform
            import subprocess
            import os

            if os.path.exists(file_path):
                system = platform.system()
                if system == "Windows":
                    # فتح الملف مع برنامج PDF الافتراضي
                    subprocess.run(['start', '', file_path], shell=True, check=False)
                elif system == "Darwin":  # macOS
                    subprocess.run(['open', file_path], check=False)
                elif system == "Linux":
                    subprocess.run(['xdg-open', file_path], check=False)
        except Exception as e:
            print(f"تحذير: لم يتم فتح الملف تلقائياً: {e}")


class BuildingDialog:
    """نافذة إضافة/تعديل مبنى"""
    
    def __init__(self, parent, db_manager, auth_manager, title, building_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.title = title
        self.building_data = building_data
        self.result = False
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.label_font = ("Segoe UI", 11)

    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("1200x600")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()

        self.create_form()
        
        # تفعيل زر الحفظ عند عرض النافذة
        if hasattr(self, 'save_button'):
            self.save_button.config(state="normal")
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
        
        self.window.wait_window()
        return self.result
    
    def create_form(self):
        """إنشاء نموذج المبنى"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=25)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # تكوين الأعمدة للتخطيط
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.columnconfigure(3, weight=1)

        # عنوان النموذج
        title_label = ttk_bs.Label(
            main_frame,
            text=f"🏢 {self.title} 🏢",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20), sticky="e")

        # الحقول مرتبة حسب الطلب
        labels = [
            ("🏢 اسم المبنى *", "name_entry", "info"),
            ("🏗️ نوع المبنى *", "type_combo", "info"),
            ("🏛️ اسم الإدارة *", "department_entry", "primary"),
            ("🏢 اسم القسم", "section_entry", "primary"),
            ("📍 الموقع", "location_entry", "secondary"),
            ("🏢 عدد الطوابق", "floors_entry", "warning"),
            ("📐 المساحة (م²)", "area_entry", "warning"),
            ("📊 الحالة الإنشائية", "status_combo", "success"),
            ("📅 سنة البناء", "year_date_entry", "info"),
            ("👤 المالك", "owner_entry", "secondary"),
            ("📝 ملاحظات", "notes_text", None)
        ]

        self.entries = {}

        # تخطيط الحقول في صفوف - كل حقلين بجوار بعض من اليمين لليسار (RTL)
        for idx, (label_text, attr_name, style) in enumerate(labels):
            row = (idx // 2) + 1
            # عكس ترتيب الأعمدة: الحقل الأول (idx=0) يذهب لليمين (col=2), الثاني (idx=1) لليسار (col=0)
            if idx % 2 == 0:  # الحقل الأول في الصف - يذهب لليمين
                col = 2
            else:  # الحقل الثاني في الصف - يذهب لليسار
                col = 0

            # في التخطيط RTL: التسمية على اليمين والحقل على اليسار
            # التسمية
            label = ttk_bs.Label(main_frame, text=label_text)
            label.grid(row=row, column=col+1, sticky="e", pady=(0, 5), padx=(10, 15))

            if attr_name == "type_combo":
                combo = ttk_bs.Combobox(main_frame, width=50, bootstyle=style)
                combo['values'] = ("إداري", "أكاديمي", "خدمي", "تقني", "ثقافي", "سكني", "تجاري")
                combo.grid(row=row, column=col, pady=(0, 15), padx=(30, 10), sticky="e")
                self.entries[attr_name] = combo
            elif attr_name == "status_combo":
                combo = ttk_bs.Combobox(main_frame, width=50, bootstyle=style)
                combo['values'] = ("ممتاز", "جيد", "متوسط", "يحتاج صيانة", "سيء")
                combo.grid(row=row, column=col, pady=(0, 15), padx=(30, 10), sticky="e")
                self.entries[attr_name] = combo
            elif attr_name == "year_date_entry":
                # استخدام منتقي التاريخ لسنة البناء
                date_entry = ttk_bs.DateEntry(main_frame, dateformat="%Y-%m-%d", width=48, bootstyle=style)
                date_entry.grid(row=row, column=col, pady=(0, 15), padx=(30, 10), sticky="e")
                self.entries[attr_name] = date_entry
            elif attr_name == "notes_text":
                # حقل الملاحظات يأخذ الصف كاملاً مع التسمية
                # إنشاء التسمية أولاً (من اليمين)
                notes_label = ttk_bs.Label(main_frame, text=label_text)
                notes_label.grid(row=row, column=3, sticky="e", pady=(0, 5), padx=(10, 15), columnspan=1)

                # إنشاء حقل النص في الصف التالي
                text = tk.Text(main_frame, height=4, width=120, wrap=tk.WORD)
                text.grid(row=row+1, column=0, columnspan=4, pady=(0, 20), padx=(15, 15), sticky="ew")
                self.entries[attr_name] = text
            else:
                entry = ttk_bs.Entry(main_frame, width=50, bootstyle=style)
                entry.grid(row=row, column=col, pady=(0, 15), padx=(30, 10), sticky="e")
                self.entries[attr_name] = entry

        # ملء البيانات إذا كان تعديل
        if self.building_data:
            self.entries["name_entry"].insert(0, self.building_data[1] or "")
            self.entries["type_combo"].set(self.building_data[2] or "")

            # ملء الحقول الجديدة حسب ترتيب الأعمدة الجديد
            if len(self.building_data) > 3:
                self.entries["department_entry"].insert(0, self.building_data[3] or "")
            if len(self.building_data) > 4:
                self.entries["section_entry"].insert(0, self.building_data[4] or "")
            if len(self.building_data) > 5:
                self.entries["location_entry"].insert(0, self.building_data[5] or "")
            if len(self.building_data) > 6:
                self.entries["floors_entry"].insert(0, str(self.building_data[6] or ""))
            if len(self.building_data) > 7:
                self.entries["area_entry"].insert(0, str(self.building_data[7] or ""))
            if len(self.building_data) > 8:
                self.entries["owner_entry"].insert(0, self.building_data[8] or "")

            # ملء تاريخ سنة البناء
            if len(self.building_data) > 9 and self.building_data[9]:
                year_str = str(self.building_data[9])
                # تحويل السنة إلى تاريخ كامل (1 يناير من تلك السنة)
                date_str = f"{year_str}-01-01"
                self.entries["year_date_entry"].entry.delete(0, tk.END)
                self.entries["year_date_entry"].entry.insert(0, date_str)

        # الأزرار
        # حساب الصف التالي بعد آخر حقل (مع مراعاة أن حقل الملاحظات يأخذ صفين)
        last_row = ((len(labels) - 1) // 2) + 3  # +3 لأن الملاحظات تأخذ صفين إضافيين
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.grid(row=last_row, column=0, columnspan=4, pady=30, sticky="ew")

        # توسيط الأزرار
        button_container = ttk_bs.Frame(button_frame)
        button_container.pack(expand=True)

        ttk_bs.Button(
            button_container,
            text="❌ إلغاء",
            command=self.window.destroy,
            bootstyle="secondary",
            width=18).pack(side=tk.RIGHT, padx=10, ipady=12)

        ttk_bs.Button(
            button_container,
            text="💾 حفظ",
            command=self.save_building,
            bootstyle="success",
            width=18).pack(side=tk.RIGHT, padx=10, ipady=12)
    
    def save_building(self):
        """حفظ بيانات المبنى"""
        try:
            name = self.entries["name_entry"].get().strip()
            building_type = self.entries["type_combo"].get().strip()
            department = self.entries["department_entry"].get().strip()
            section = self.entries["section_entry"].get().strip()
            location = self.entries["location_entry"].get().strip()
            floors = self.entries["floors_entry"].get().strip()
            area = self.entries["area_entry"].get().strip()
            status = self.entries["status_combo"].get().strip()
            # قراءة التاريخ من منتقي التاريخ واستخراج السنة
            selected_date = self.entries["year_date_entry"].entry.get().strip()
            year = ""
            if selected_date:
                try:
                    # استخراج السنة من التاريخ
                    year = selected_date.split('-')[0]
                except:
                    year = ""
            owner = self.entries["owner_entry"].get().strip()
            notes = self.entries["notes_text"].get('1.0', tk.END).strip()
        except KeyError as e:
            messagebox.showerror("خطأ", f"خطأ في الوصول إلى الحقل: {e}")
            return
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة البيانات: {e}")
            return

        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المبنى")
            return

        if not building_type:
            messagebox.showerror("خطأ", "يرجى اختيار نوع المبنى")
            return

        if not department:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الإدارة")
            return
        
        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            # تحويل القيم الرقمية
            floors_int = None
            if floors:
                try:
                    floors_int = int(floors)
                except ValueError:
                    pass

            area_float = None
            if area:
                try:
                    area_float = float(area)
                except ValueError:
                    pass

            year_int = None
            if year:
                try:
                    year_int = int(year)
                except ValueError:
                    pass

            # أولاً، التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
            try:
                cursor.execute("PRAGMA table_info(buildings)")
                columns = [column[1] for column in cursor.fetchall()]

                if 'department_name' not in columns:
                    cursor.execute("ALTER TABLE buildings ADD COLUMN department_name TEXT")
                if 'section_name' not in columns:
                    cursor.execute("ALTER TABLE buildings ADD COLUMN section_name TEXT")

                conn.commit()
            except Exception as e:
                print(f"تحذير: لم يتم إضافة الأعمدة الجديدة: {e}")

            if self.building_data:  # تعديل
                cursor.execute('''
                    UPDATE buildings
                    SET name=?, building_type=?, department_name=?, section_name=?, location=?, floors=?, area=?,
                        construction_year=?, owner=?, structural_condition=?, notes=?
                    WHERE id=?
                ''', (name, building_type, department, section, location, floors_int, area_float,
                      year_int, owner, status, notes, self.building_data[0]))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO buildings (name, building_type, department_name, section_name, location, floors, area,
                                         construction_year, owner, structural_condition, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (name, building_type, department, section, location, floors_int, area_float,
                      year_int, owner, status, notes))
            
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بيانات المبنى بنجاح")
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()





    def create_buildings_pdf_report(self, buildings_data, file_path):
        """إنشاء تقرير PDF للمباني"""
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT
        from datetime import datetime

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )

        # إنشاء الأنماط
        styles = getSampleStyleSheet()

        # نمط العنوان
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )

        # نمط العنوان الفرعي
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_RIGHT,
            textColor=colors.darkgreen
        )

        # قائمة العناصر
        story = []

        # العنوان الرئيسي
        title_text = self.format_arabic_text("تقرير المباني والمرافق")
        story.append(Paragraph(title_text, title_style))
        story.append(Spacer(1, 20))

        # التاريخ
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        date_text = self.format_arabic_text(f"تاريخ التقرير {current_time}")
        story.append(Paragraph(date_text, styles['Normal']))
        story.append(Spacer(1, 20))

        # إحصائيات سريعة
        total_buildings = len(buildings_data)
        total_area = sum([b['area'] for b in buildings_data])
        avg_floors = sum([b['floors'] for b in buildings_data]) / total_buildings if total_buildings > 0 else 0

        stats_title = self.format_arabic_text("إحصائيات المباني")
        story.append(Paragraph(stats_title, heading_style))

        stat1 = self.format_arabic_text(f"- إجمالي المباني: {total_buildings}")
        story.append(Paragraph(stat1, styles['Normal']))

        stat2 = self.format_arabic_text(f"- إجمالي المساحة: {total_area:,.0f} متر مربع")
        story.append(Paragraph(stat2, styles['Normal']))

        stat3 = self.format_arabic_text(f"- متوسط عدد الطوابق: {avg_floors:.1f}")
        story.append(Paragraph(stat3, styles['Normal']))
        story.append(Spacer(1, 20))

        # جدول المباني
        table_title = self.format_arabic_text("تفاصيل المباني")
        story.append(Paragraph(table_title, heading_style))

        # إنشاء بيانات الجدول
        headers = [
            self.format_arabic_text('اسم المبنى'),
            self.format_arabic_text('النوع'),
            self.format_arabic_text('الطوابق'),
            self.format_arabic_text('المساحة'),
            self.format_arabic_text('الحالة')
        ]
        table_data = [headers]

        for building in buildings_data:
            name = self.format_arabic_text(str(building['name']))
            building_type = self.format_arabic_text(str(building['building_type']))
            floors = self.format_arabic_text(str(building['floors']))
            area = self.format_arabic_text(f"{building['area']:,.0f} م²")
            condition = self.format_arabic_text(str(building['structural_condition']))

            table_data.append([name, building_type, floors, area, condition])

        # إنشاء الجدول
        table = Table(table_data, repeatRows=1)
        table.setStyle(TableStyle([
            # تنسيق الرأس
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

            # تنسيق البيانات
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        story.append(table)

        # بناء المستند
        doc.build(story)
